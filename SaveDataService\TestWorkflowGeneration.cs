using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using GameServer;
using GameServer.Util;

namespace SaveDataService
{
    /// <summary>
    /// 工作流类生成器
    /// </summary>
    public class TestWorkflowGeneration
    {
        /// <summary>
        /// 工作流分析结果
        /// </summary>
        public class WorkflowAnalysis
        {
            public List<NodeInfo> AllNodes { get; set; } = new List<NodeInfo>();
            public List<NodeInfo> UserInputNodes { get; set; } = new List<NodeInfo>();
            public string WorkflowType { get; set; } = "";
            public string Description { get; set; } = "";
        }

        /// <summary>
        /// 节点信息
        /// </summary>
        public class NodeInfo
        {
            public string NodeId { get; set; } = "";
            public string ClassType { get; set; } = "";
            public string Title { get; set; } = "";
            public Dictionary<string, object> Inputs { get; set; } = new Dictionary<string, object>();
            public List<UserInput> UserInputs { get; set; } = new List<UserInput>();
        }

        /// <summary>
        /// 用户输入信息
        /// </summary>
        public class UserInput
        {
            public string Name { get; set; } = "";
            public string Type { get; set; } = "";
            public object DefaultValue { get; set; } = "";
            public string Description { get; set; } = "";
            public bool IsRequired { get; set; } = true;
        }

        /// <summary>
        /// 需要用户输入的节点类型
        /// </summary>
        private static readonly Dictionary<string, List<string>> UserInputNodeTypes = new Dictionary<string, List<string>>
        {
            ["CLIPTextEncode"] = new List<string> { "text" },
            ["LoadImage"] = new List<string> { "image" },
            ["EmptyLatentImage"] = new List<string> { "width", "height", "batch_size" },
            ["KSampler"] = new List<string> { "seed", "steps", "cfg", "sampler_name", "scheduler", "denoise" },
            ["SaveImage"] = new List<string> { "filename_prefix" },
            ["CheckpointLoaderSimple"] = new List<string> { "ckpt_name" },
            ["VAELoader"] = new List<string> { "vae_name" },
            ["UNETLoader"] = new List<string> { "unet_name" },
            ["DualCLIPLoader"] = new List<string> { "clip_name1", "clip_name2" },
            ["UpscaleModelLoader"] = new List<string> { "model_name" },
            ["FluxGuidance"] = new List<string> { "guidance" },
            ["BasicScheduler"] = new List<string> { "scheduler", "steps", "denoise" },
            ["SamplerCustom"] = new List<string> { "noise_seed", "cfg" },
            ["KSamplerSelect"] = new List<string> { "sampler_name" }
        };

        /// <summary>
        /// 测试工作流生成功能
        /// </summary>
        public static void RunTest()
        {
            Console.WriteLine("=== 工作流类生成测试 ===");

            try
            {

                
                string workflowDir = PatchUtil.ResRootPatch + AppConfig.Instance.workflowsPath;



                // 1. 扫描工作流文件夹
                
                if (!Directory.Exists(workflowDir))
                {
                    Console.WriteLine($"工作流目录不存在: {workflowDir}");
                    return;
                }

                var jsonFiles = Directory.GetFiles(workflowDir, "*.json", SearchOption.AllDirectories);
                Console.WriteLine($"找到 {jsonFiles.Length} 个工作流文件");

                // 2. 创建输出目录
                string outputDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ComfyuiGate");
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }
                Console.WriteLine($"输出目录 {outputDir} ");
                // 3. 为每个工作流生成类
                int successCount = 0;
                foreach (var jsonFile in jsonFiles)
                {
                    try
                    {
                        Console.WriteLine($"\n处理工作流: {Path.GetFileName(jsonFile)}");
                        if (GenerateWorkflowClass(jsonFile, outputDir))
                        {
                            successCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件 {jsonFile} 时出错: {ex.Message}");
                    }
                }

                Console.WriteLine($"\n=== 工作流类生成完成，成功生成 {successCount} 个类 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"工作流类生成测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为单个工作流生成类
        /// </summary>
        private static bool GenerateWorkflowClass(string jsonFilePath, string outputDir)
        {
            try
            {
                // 读取JSON文件
                string jsonContent = File.ReadAllText(jsonFilePath);

                // 解析工作流
                var workflowAnalysis = AnalyzeWorkflow(jsonContent);
                if (workflowAnalysis == null)
                {
                    Console.WriteLine("无法分析工作流");
                    return false;
                }

                // 生成类名
                string fileName = Path.GetFileNameWithoutExtension(jsonFilePath);
                string className = GenerateClassName(fileName);

                Console.WriteLine($"生成类: {className}");
                Console.WriteLine($"  - 需要用户输入的节点: {workflowAnalysis.UserInputNodes.Count}");
                Console.WriteLine($"  - 总节点数: {workflowAnalysis.AllNodes.Count}");

                // 生成工作流包装类
                string wrapperClass = GenerateWrapperClass(className, workflowAnalysis, jsonContent);
                string wrapperFilePath = Path.Combine(outputDir, $"{className}.cs");
                File.WriteAllText(wrapperFilePath, wrapperClass);

                // 生成测试类
                string testClass = GenerateTestClass(className, workflowAnalysis);
                string testFilePath = Path.Combine(outputDir, $"{className}Test.cs");
                File.WriteAllText(testFilePath, testClass);

                Console.WriteLine($"  - 生成文件: {wrapperFilePath}");
                Console.WriteLine($"  - 生成文件: {testFilePath}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成类失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 分析工作流
        /// </summary>
        private static WorkflowAnalysis AnalyzeWorkflow(string jsonContent)
        {
            try
            {
                var analysis = new WorkflowAnalysis();

                // 解析JSON
                JObject workflow;
                try
                {
                    workflow = JObject.Parse(jsonContent);
                }
                catch
                {
                    Console.WriteLine("JSON解析失败");
                    return null;
                }

                // 分析每个节点
                foreach (var kvp in workflow)
                {
                    if (kvp.Value is JObject nodeObj)
                    {
                        var nodeInfo = AnalyzeNode(kvp.Key, nodeObj);
                        analysis.AllNodes.Add(nodeInfo);

                        if (nodeInfo.UserInputs.Count > 0)
                        {
                            analysis.UserInputNodes.Add(nodeInfo);
                        }
                    }
                }

                // 推断工作流类型
                analysis.WorkflowType = InferWorkflowType(analysis.AllNodes);
                analysis.Description = GenerateDescription(analysis.AllNodes, analysis.UserInputNodes);

                return analysis;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析工作流失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 分析单个节点
        /// </summary>
        private static NodeInfo AnalyzeNode(string nodeId, JObject nodeObj)
        {
            var nodeInfo = new NodeInfo
            {
                NodeId = nodeId,
                ClassType = nodeObj["class_type"]?.ToString() ?? "",
                Title = nodeObj["_meta"]?["title"]?.ToString() ?? nodeId
            };

            // 解析输入参数
            if (nodeObj["inputs"] is JObject inputs)
            {
                foreach (var input in inputs)
                {
                    nodeInfo.Inputs[input.Key] = input.Value?.ToString() ?? "";
                }
            }

            // 检查是否需要用户输入
            if (UserInputNodeTypes.ContainsKey(nodeInfo.ClassType))
            {
                var userInputFields = UserInputNodeTypes[nodeInfo.ClassType];
                foreach (var field in userInputFields)
                {
                    if (nodeInfo.Inputs.ContainsKey(field))
                    {
                        var userInput = new UserInput
                        {
                            Name = field,
                            Type = GetInputType(nodeInfo.ClassType, field),
                            DefaultValue = nodeInfo.Inputs[field],
                            Description = GetInputDescription(nodeInfo.ClassType, field),
                            IsRequired = IsRequiredInput(nodeInfo.ClassType, field)
                        };
                        nodeInfo.UserInputs.Add(userInput);
                    }
                }
            }

            return nodeInfo;
        }

        /// <summary>
        /// 获取输入类型
        /// </summary>
        private static string GetInputType(string classType, string fieldName)
        {
            var typeMap = new Dictionary<string, string>
            {
                ["text"] = "string",
                ["image"] = "string", // 图片路径
                ["width"] = "int",
                ["height"] = "int",
                ["batch_size"] = "int",
                ["seed"] = "int",
                ["steps"] = "int",
                ["cfg"] = "double",
                ["sampler_name"] = "string",
                ["scheduler"] = "string",
                ["denoise"] = "double",
                ["filename_prefix"] = "string",
                ["ckpt_name"] = "string",
                ["vae_name"] = "string",
                ["unet_name"] = "string",
                ["clip_name1"] = "string",
                ["clip_name2"] = "string",
                ["model_name"] = "string",
                ["guidance"] = "double",
                ["noise_seed"] = "int"
            };

            return typeMap.ContainsKey(fieldName) ? typeMap[fieldName] : "string";
        }

        /// <summary>
        /// 获取输入描述
        /// </summary>
        private static string GetInputDescription(string classType, string fieldName)
        {
            var descMap = new Dictionary<string, string>
            {
                ["text"] = "文本提示词",
                ["image"] = "输入图片路径",
                ["width"] = "图片宽度",
                ["height"] = "图片高度",
                ["batch_size"] = "批次大小",
                ["seed"] = "随机种子",
                ["steps"] = "采样步数",
                ["cfg"] = "CFG引导强度",
                ["sampler_name"] = "采样器名称",
                ["scheduler"] = "调度器",
                ["denoise"] = "去噪强度",
                ["filename_prefix"] = "文件名前缀",
                ["ckpt_name"] = "检查点模型名称",
                ["vae_name"] = "VAE模型名称",
                ["unet_name"] = "UNet模型名称",
                ["clip_name1"] = "CLIP模型1名称",
                ["clip_name2"] = "CLIP模型2名称",
                ["model_name"] = "模型名称",
                ["guidance"] = "引导强度",
                ["noise_seed"] = "噪声种子"
            };

            return descMap.ContainsKey(fieldName) ? descMap[fieldName] : fieldName;
        }

        /// <summary>
        /// 判断是否为必需输入
        /// </summary>
        private static bool IsRequiredInput(string classType, string fieldName)
        {
            var requiredFields = new HashSet<string> { "text", "image", "width", "height" };
            return requiredFields.Contains(fieldName);
        }

        /// <summary>
        /// 推断工作流类型
        /// </summary>
        private static string InferWorkflowType(List<NodeInfo> nodes)
        {
            var nodeTypes = nodes.Select(n => n.ClassType).ToHashSet();

            if (nodeTypes.Contains("LoadImage"))
                return "图片处理";
            else if (nodeTypes.Contains("EmptyLatentImage"))
                return "文生图";
            else if (nodeTypes.Contains("UpscaleModelLoader"))
                return "图片放大";
            else
                return "通用工作流";
        }

        /// <summary>
        /// 生成描述
        /// </summary>
        private static string GenerateDescription(List<NodeInfo> allNodes, List<NodeInfo> userInputNodes)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"包含 {allNodes.Count} 个节点的ComfyUI工作流");
            sb.AppendLine($"需要用户输入 {userInputNodes.Count} 个参数");

            if (userInputNodes.Count > 0)
            {
                sb.AppendLine("用户输入参数:");
                foreach (var node in userInputNodes)
                {
                    foreach (var input in node.UserInputs)
                    {
                        sb.AppendLine($"  - {input.Description} ({input.Type})");
                    }
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 生成类名
        /// </summary>
        private static string GenerateClassName(string fileName)
        {
            // 移除特殊字符，转换为Pascal命名
            var sb = new StringBuilder();
            bool capitalizeNext = true;

            foreach (char c in fileName)
            {
                if (char.IsLetterOrDigit(c))
                {
                    if (capitalizeNext)
                    {
                        sb.Append(char.ToUpper(c));
                        capitalizeNext = false;
                    }
                    else
                    {
                        sb.Append(c);
                    }
                }
                else
                {
                    capitalizeNext = true;
                }
            }

            string result = sb.ToString();
            if (string.IsNullOrEmpty(result))
            {
                result = "UnknownWorkflow";
            }

            return result + "Workflow";
        }

        /// <summary>
        /// 生成包装类代码
        /// </summary>
        private static string GenerateWrapperClass(string className, WorkflowAnalysis analysis, string originalJson)
        {
            var sb = new StringBuilder();

            // 文件头
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Collections.Generic;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using Newtonsoft.Json;");
            sb.AppendLine("using Newtonsoft.Json.Linq;");
            sb.AppendLine("using SaveDataService.Manage;");
            sb.AppendLine();
            sb.AppendLine("namespace SaveDataService.ComfyuiGate");
            sb.AppendLine("{");

            // 类注释
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {className} - {analysis.WorkflowType}");
            sb.AppendLine($"    /// {analysis.Description.Replace("\n", "\n    /// ")}");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {className}");
            sb.AppendLine("    {");

            // 输入参数类
            if (analysis.UserInputNodes.Count > 0)
            {
                sb.AppendLine("        /// <summary>");
                sb.AppendLine("        /// 工作流输入参数");
                sb.AppendLine("        /// </summary>");
                sb.AppendLine("        public class WorkflowInputs");
                sb.AppendLine("        {");

                foreach (var node in analysis.UserInputNodes)
                {
                    foreach (var input in node.UserInputs)
                    {
                        sb.AppendLine($"            /// <summary>");
                        sb.AppendLine($"            /// {input.Description}");
                        sb.AppendLine($"            /// </summary>");
                        sb.AppendLine($"            public {input.Type} {ToPascalCase(input.Name)} {{ get; set; }} = {GetDefaultValue(input.Type, input.DefaultValue)};");
                        sb.AppendLine();
                    }
                }

                sb.AppendLine("        }");
                sb.AppendLine();
            }

            // 原始工作流JSON
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 原始工作流JSON");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        private static readonly string OriginalWorkflowJson = @\"");
            sb.AppendLine(originalJson.Replace("\"", "\"\""));
            sb.AppendLine("        \";");
            sb.AppendLine();

            // 执行方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 执行工作流");
            sb.AppendLine("        /// </summary>");
            if (analysis.UserInputNodes.Count > 0)
            {
                sb.AppendLine("        public static async Task<string> ExecuteAsync(WorkflowInputs inputs, string serverUrl = null)");
            }
            else
            {
                sb.AppendLine("        public static async Task<string> ExecuteAsync(string serverUrl = null)");
            }
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine("                // 解析原始工作流");
            sb.AppendLine("                var workflow = JObject.Parse(OriginalWorkflowJson);");
            sb.AppendLine();

            // 参数替换逻辑
            if (analysis.UserInputNodes.Count > 0)
            {
                sb.AppendLine("                // 替换用户输入参数");
                foreach (var node in analysis.UserInputNodes)
                {
                    foreach (var input in node.UserInputs)
                    {
                        sb.AppendLine($"                if (workflow[\"{node.NodeId}\"]?[\"inputs\"]?[\"{input.Name}\"] != null)");
                        sb.AppendLine($"                    workflow[\"{node.NodeId}\"][\"inputs\"][\"{input.Name}\"] = inputs.{ToPascalCase(input.Name)};");
                        sb.AppendLine();
                    }
                }
            }

            sb.AppendLine("                // 使用ComfyUIManage执行工作流");
            sb.AppendLine("                var comfyUIManage = new ComfyUIManage();");
            sb.AppendLine("                var result = await comfyUIManage.ExecuteWorkflowAsync(workflow.ToString(), serverUrl);");
            sb.AppendLine("                return result;");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine($"                throw new Exception($\"{className}执行失败: {{ex.Message}}\", ex);");
            sb.AppendLine("            }");
            sb.AppendLine("        }");

            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 转换为Pascal命名
        /// </summary>
        private static string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var sb = new StringBuilder();
            bool capitalizeNext = true;

            foreach (char c in input)
            {
                if (char.IsLetterOrDigit(c))
                {
                    if (capitalizeNext)
                    {
                        sb.Append(char.ToUpper(c));
                        capitalizeNext = false;
                    }
                    else
                    {
                        sb.Append(c);
                    }
                }
                else
                {
                    capitalizeNext = true;
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 获取默认值
        /// </summary>
        private static string GetDefaultValue(string type, object defaultValue)
        {
            switch (type.ToLower())
            {
                case "int":
                    return defaultValue?.ToString() ?? "0";
                case "double":
                    return defaultValue?.ToString() ?? "0.0";
                case "bool":
                    return defaultValue?.ToString()?.ToLower() ?? "false";
                case "string":
                default:
                    return $"\"{defaultValue?.ToString() ?? ""}\"";
            }
        }

        /// <summary>
        /// 生成测试类代码
        /// </summary>
        private static string GenerateTestClass(string className, WorkflowAnalysis analysis)
        {
            var sb = new StringBuilder();

            // 文件头
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using SaveDataService.ComfyuiGate;");
            sb.AppendLine();
            sb.AppendLine("namespace SaveDataService.ComfyuiGate");
            sb.AppendLine("{");

            // 类注释
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {className}测试类");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {className}Test");
            sb.AppendLine("    {");

            // 测试方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 测试工作流执行");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        public static async Task RunTestAsync()");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine($"                Console.WriteLine(\"=== {className} 测试开始 ===\");");
            sb.AppendLine();

            if (analysis.UserInputNodes.Count > 0)
            {
                sb.AppendLine("                // 创建测试输入参数");
                sb.AppendLine($"                var inputs = new {className}.WorkflowInputs");
                sb.AppendLine("                {");

                foreach (var node in analysis.UserInputNodes)
                {
                    foreach (var input in node.UserInputs)
                    {
                        sb.AppendLine($"                    {ToPascalCase(input.Name)} = {GetTestValue(input.Type, input.Name)},");
                    }
                }

                sb.AppendLine("                };");
                sb.AppendLine();
                sb.AppendLine("                // 执行工作流");
                sb.AppendLine($"                var result = await {className}.ExecuteAsync(inputs);");
            }
            else
            {
                sb.AppendLine("                // 执行工作流");
                sb.AppendLine($"                var result = await {className}.ExecuteAsync();");
            }

            sb.AppendLine("                Console.WriteLine($\"工作流执行结果: {result}\");");
            sb.AppendLine($"                Console.WriteLine(\"=== {className} 测试完成 ===\");");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine($"                Console.WriteLine($\"{className} 测试失败: {{ex.Message}}\");");
            sb.AppendLine("            }");
            sb.AppendLine("        }");

            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 获取测试值
        /// </summary>
        private static string GetTestValue(string type, string fieldName)
        {
            switch (type.ToLower())
            {
                case "int":
                    if (fieldName.Contains("width")) return "512";
                    if (fieldName.Contains("height")) return "512";
                    if (fieldName.Contains("steps")) return "20";
                    if (fieldName.Contains("seed")) return "123456";
                    if (fieldName.Contains("batch")) return "1";
                    return "1";
                case "double":
                    if (fieldName.Contains("cfg")) return "7.0";
                    if (fieldName.Contains("denoise")) return "1.0";
                    if (fieldName.Contains("guidance")) return "3.5";
                    return "1.0";
                case "bool":
                    return "true";
                case "string":
                default:
                    if (fieldName.Contains("text")) return "\"a beautiful landscape\"";
                    if (fieldName.Contains("image")) return "\"test_image.png\"";
                    if (fieldName.Contains("sampler")) return "\"euler\"";
                    if (fieldName.Contains("scheduler")) return "\"normal\"";
                    if (fieldName.Contains("filename")) return "\"test_output\"";
                    if (fieldName.Contains("model") || fieldName.Contains("ckpt")) return "\"test_model.safetensors\"";
                    return "\"test_value\"";
            }
        }
    }
}
