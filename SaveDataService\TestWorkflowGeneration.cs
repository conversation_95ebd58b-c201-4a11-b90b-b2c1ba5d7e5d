using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using GameServer;
using GameServer.Util;

namespace SaveDataService
{
    /// <summary>
    /// 工作流类生成器
    /// </summary>
    public class TestWorkflowGeneration
    {
        /// <summary>
        /// 工作流分析结果
        /// </summary>
        public class WorkflowAnalysis
        {
            public List<NodeInfo> AllNodes { get; set; } = new List<NodeInfo>();
            public List<NodeInfo> UserInputNodes { get; set; } = new List<NodeInfo>();
            public string WorkflowType { get; set; } = "";
            public string Description { get; set; } = "";
        }

        /// <summary>
        /// 节点信息
        /// </summary>
        public class NodeInfo
        {
            public string NodeId { get; set; } = "";
            public string ClassType { get; set; } = "";
            public string Title { get; set; } = "";
            public Dictionary<string, object> Inputs { get; set; } = new Dictionary<string, object>();
            public List<UserInput> UserInputs { get; set; } = new List<UserInput>();
        }

        /// <summary>
        /// 用户输入信息
        /// </summary>
        public class UserInput
        {
            public string Name { get; set; } = "";
            public string Type { get; set; } = "";
            public object DefaultValue { get; set; } = "";
            public string Description { get; set; } = "";
            public bool IsRequired { get; set; } = true;
        }

        /// <summary>
        /// ComfyUI节点类型定义
        /// </summary>
        private static readonly Dictionary<string, NodeTypeDefinition> NodeTypeDefinitions = new Dictionary<string, NodeTypeDefinition>
        {
            ["CLIPTextEncode"] = new NodeTypeDefinition
            {
                Description = "CLIP文本编码器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["text"] = new ParameterDefinition { Type = "string", Description = "文本提示词", IsRequired = true, DefaultValue = "" }
                }
            },
            ["LoadImage"] = new NodeTypeDefinition
            {
                Description = "加载图片",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["image"] = new ParameterDefinition { Type = "string", Description = "图片路径", IsRequired = true, DefaultValue = "" }
                }
            },
            ["EmptyLatentImage"] = new NodeTypeDefinition
            {
                Description = "创建空白潜在图像",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["width"] = new ParameterDefinition { Type = "int", Description = "图片宽度", IsRequired = true, DefaultValue = 512 },
                    ["height"] = new ParameterDefinition { Type = "int", Description = "图片高度", IsRequired = true, DefaultValue = 512 },
                    ["batch_size"] = new ParameterDefinition { Type = "int", Description = "批次大小", IsRequired = false, DefaultValue = 1 }
                }
            },
            ["KSampler"] = new NodeTypeDefinition
            {
                Description = "K采样器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["seed"] = new ParameterDefinition { Type = "int", Description = "随机种子", IsRequired = false, DefaultValue = -1 },
                    ["steps"] = new ParameterDefinition { Type = "int", Description = "采样步数", IsRequired = true, DefaultValue = 20 },
                    ["cfg"] = new ParameterDefinition { Type = "double", Description = "CFG引导强度", IsRequired = true, DefaultValue = 7.0 },
                    ["sampler_name"] = new ParameterDefinition { Type = "string", Description = "采样器名称", IsRequired = true, DefaultValue = "euler" },
                    ["scheduler"] = new ParameterDefinition { Type = "string", Description = "调度器", IsRequired = true, DefaultValue = "normal" },
                    ["denoise"] = new ParameterDefinition { Type = "double", Description = "去噪强度", IsRequired = false, DefaultValue = 1.0 }
                }
            },
            ["SaveImage"] = new NodeTypeDefinition
            {
                Description = "保存图片",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["filename_prefix"] = new ParameterDefinition { Type = "string", Description = "文件名前缀", IsRequired = false, DefaultValue = "ComfyUI" }
                }
            },
            ["CheckpointLoaderSimple"] = new NodeTypeDefinition
            {
                Description = "简单检查点加载器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["ckpt_name"] = new ParameterDefinition { Type = "string", Description = "检查点模型名称", IsRequired = true, DefaultValue = "" }
                }
            },
            ["VAELoader"] = new NodeTypeDefinition
            {
                Description = "VAE加载器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["vae_name"] = new ParameterDefinition { Type = "string", Description = "VAE模型名称", IsRequired = true, DefaultValue = "" }
                }
            },
            ["UNETLoader"] = new NodeTypeDefinition
            {
                Description = "UNet加载器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["unet_name"] = new ParameterDefinition { Type = "string", Description = "UNet模型名称", IsRequired = true, DefaultValue = "" }
                }
            },
            ["DualCLIPLoader"] = new NodeTypeDefinition
            {
                Description = "双CLIP加载器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["clip_name1"] = new ParameterDefinition { Type = "string", Description = "CLIP模型1名称", IsRequired = true, DefaultValue = "" },
                    ["clip_name2"] = new ParameterDefinition { Type = "string", Description = "CLIP模型2名称", IsRequired = true, DefaultValue = "" }
                }
            },
            ["UpscaleModelLoader"] = new NodeTypeDefinition
            {
                Description = "放大模型加载器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["model_name"] = new ParameterDefinition { Type = "string", Description = "放大模型名称", IsRequired = true, DefaultValue = "" }
                }
            },
            ["FluxGuidance"] = new NodeTypeDefinition
            {
                Description = "Flux引导",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["guidance"] = new ParameterDefinition { Type = "double", Description = "引导强度", IsRequired = true, DefaultValue = 3.5 }
                }
            },
            ["BasicScheduler"] = new NodeTypeDefinition
            {
                Description = "基础调度器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["scheduler"] = new ParameterDefinition { Type = "string", Description = "调度器类型", IsRequired = true, DefaultValue = "normal" },
                    ["steps"] = new ParameterDefinition { Type = "int", Description = "步数", IsRequired = true, DefaultValue = 20 },
                    ["denoise"] = new ParameterDefinition { Type = "double", Description = "去噪强度", IsRequired = false, DefaultValue = 1.0 }
                }
            },
            ["SamplerCustom"] = new NodeTypeDefinition
            {
                Description = "自定义采样器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["noise_seed"] = new ParameterDefinition { Type = "int", Description = "噪声种子", IsRequired = false, DefaultValue = -1 },
                    ["cfg"] = new ParameterDefinition { Type = "double", Description = "CFG引导强度", IsRequired = true, DefaultValue = 7.0 }
                }
            },
            ["KSamplerSelect"] = new NodeTypeDefinition
            {
                Description = "K采样器选择",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["sampler_name"] = new ParameterDefinition { Type = "string", Description = "采样器名称", IsRequired = true, DefaultValue = "euler" }
                }
            },
            ["VAEDecode"] = new NodeTypeDefinition
            {
                Description = "VAE解码器",
                Parameters = new Dictionary<string, ParameterDefinition>()
            },
            ["VAEEncode"] = new NodeTypeDefinition
            {
                Description = "VAE编码器",
                Parameters = new Dictionary<string, ParameterDefinition>()
            },
            ["LoraLoader"] = new NodeTypeDefinition
            {
                Description = "LoRA加载器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["lora_name"] = new ParameterDefinition { Type = "string", Description = "LoRA模型名称", IsRequired = true, DefaultValue = "" },
                    ["strength_model"] = new ParameterDefinition { Type = "double", Description = "模型强度", IsRequired = false, DefaultValue = 1.0 },
                    ["strength_clip"] = new ParameterDefinition { Type = "double", Description = "CLIP强度", IsRequired = false, DefaultValue = 1.0 }
                }
            },
            ["ControlNetLoader"] = new NodeTypeDefinition
            {
                Description = "ControlNet加载器",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["control_net_name"] = new ParameterDefinition { Type = "string", Description = "ControlNet模型名称", IsRequired = true, DefaultValue = "" }
                }
            },
            ["ControlNetApply"] = new NodeTypeDefinition
            {
                Description = "ControlNet应用",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["strength"] = new ParameterDefinition { Type = "double", Description = "控制强度", IsRequired = false, DefaultValue = 1.0 },
                    ["start_percent"] = new ParameterDefinition { Type = "double", Description = "开始百分比", IsRequired = false, DefaultValue = 0.0 },
                    ["end_percent"] = new ParameterDefinition { Type = "double", Description = "结束百分比", IsRequired = false, DefaultValue = 1.0 }
                }
            },
            ["ImageScale"] = new NodeTypeDefinition
            {
                Description = "图片缩放",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["upscale_method"] = new ParameterDefinition { Type = "string", Description = "放大方法", IsRequired = true, DefaultValue = "nearest-exact" },
                    ["width"] = new ParameterDefinition { Type = "int", Description = "目标宽度", IsRequired = true, DefaultValue = 512 },
                    ["height"] = new ParameterDefinition { Type = "int", Description = "目标高度", IsRequired = true, DefaultValue = 512 },
                    ["crop"] = new ParameterDefinition { Type = "string", Description = "裁剪方式", IsRequired = false, DefaultValue = "disabled" }
                }
            },
            ["ImageUpscaleWithModel"] = new NodeTypeDefinition
            {
                Description = "使用模型放大图片",
                Parameters = new Dictionary<string, ParameterDefinition>()
            },
            ["PreviewImage"] = new NodeTypeDefinition
            {
                Description = "预览图片",
                Parameters = new Dictionary<string, ParameterDefinition>()
            },
            ["CLIPSetLastLayer"] = new NodeTypeDefinition
            {
                Description = "设置CLIP最后层",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["stop_at_clip_layer"] = new ParameterDefinition { Type = "int", Description = "停止在CLIP层", IsRequired = true, DefaultValue = -1 }
                }
            },
            ["FreeU"] = new NodeTypeDefinition
            {
                Description = "FreeU",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["b1"] = new ParameterDefinition { Type = "double", Description = "B1参数", IsRequired = true, DefaultValue = 1.1 },
                    ["b2"] = new ParameterDefinition { Type = "double", Description = "B2参数", IsRequired = true, DefaultValue = 1.2 },
                    ["s1"] = new ParameterDefinition { Type = "double", Description = "S1参数", IsRequired = true, DefaultValue = 0.9 },
                    ["s2"] = new ParameterDefinition { Type = "double", Description = "S2参数", IsRequired = true, DefaultValue = 0.2 }
                }
            },
            ["ModelSamplingDiscrete"] = new NodeTypeDefinition
            {
                Description = "离散模型采样",
                Parameters = new Dictionary<string, ParameterDefinition>
                {
                    ["sampling"] = new ParameterDefinition { Type = "string", Description = "采样方式", IsRequired = true, DefaultValue = "eps" },
                    ["zsnr"] = new ParameterDefinition { Type = "bool", Description = "零信噪比", IsRequired = false, DefaultValue = false }
                }
            }
        };

        /// <summary>
        /// 节点类型定义
        /// </summary>
        public class NodeTypeDefinition
        {
            public string Description { get; set; } = "";
            public Dictionary<string, ParameterDefinition> Parameters { get; set; } = new Dictionary<string, ParameterDefinition>();
        }

        /// <summary>
        /// 参数定义
        /// </summary>
        public class ParameterDefinition
        {
            public string Type { get; set; } = "string";
            public string Description { get; set; } = "";
            public bool IsRequired { get; set; } = true;
            public object DefaultValue { get; set; } = "";
        }

        /// <summary>
        /// 测试工作流生成功能
        /// </summary>
        public static void RunTest()
        {
            Console.WriteLine("=== 工作流类生成测试 ===");

            try
            {

                
                string workflowDir = PatchUtil.ResRootPatch + AppConfig.Instance.workflowsPath;



                // 1. 扫描工作流文件夹
                
                if (!Directory.Exists(workflowDir))
                {
                    Console.WriteLine($"工作流目录不存在: {workflowDir}");
                    return;
                }

                var jsonFiles = Directory.GetFiles(workflowDir, "*.json", SearchOption.AllDirectories);
                Console.WriteLine($"找到 {jsonFiles.Length} 个工作流文件");

                // 2. 创建输出目录
                string outputDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ComfyuiGate");
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }
                Console.WriteLine($"输出目录 {outputDir} ");
                // 3. 为每个工作流生成类
                int successCount = 0;
                foreach (var jsonFile in jsonFiles)
                {
                    try
                    {
                        Console.WriteLine($"\n处理工作流: {Path.GetFileName(jsonFile)}");
                        if (GenerateWorkflowClass(jsonFile, outputDir))
                        {
                            successCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件 {jsonFile} 时出错: {ex.Message}");
                    }
                }

                Console.WriteLine($"\n=== 工作流类生成完成，成功生成 {successCount} 个类 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"工作流类生成测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为单个工作流生成类
        /// </summary>
        private static bool GenerateWorkflowClass(string jsonFilePath, string outputDir)
        {
            try
            {
                // 读取JSON文件
                string jsonContent = File.ReadAllText(jsonFilePath);

                // 解析工作流
                var workflowAnalysis = AnalyzeWorkflow(jsonContent);
                if (workflowAnalysis == null)
                {
                    Console.WriteLine("无法分析工作流");
                    return false;
                }

                // 生成类名
                string fileName = Path.GetFileNameWithoutExtension(jsonFilePath);
                string className = GenerateClassName(fileName);

                Console.WriteLine($"生成类: {className}");
                Console.WriteLine($"  - 需要用户输入的节点: {workflowAnalysis.UserInputNodes.Count}");
                Console.WriteLine($"  - 总节点数: {workflowAnalysis.AllNodes.Count}");

                // 生成工作流包装类
                string wrapperClass = GenerateWrapperClass(className, workflowAnalysis, jsonContent);
                string wrapperFilePath = Path.Combine(outputDir, $"{className}.cs");
                File.WriteAllText(wrapperFilePath, wrapperClass);

                // 生成测试类
                string testClass = GenerateTestClass(className, workflowAnalysis);
                string testFilePath = Path.Combine(outputDir, $"{className}Test.cs");
                File.WriteAllText(testFilePath, testClass);

                Console.WriteLine($"  - 生成文件: {wrapperFilePath}");
                Console.WriteLine($"  - 生成文件: {testFilePath}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成类失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 分析工作流
        /// </summary>
        private static WorkflowAnalysis AnalyzeWorkflow(string jsonContent)
        {
            try
            {
                var analysis = new WorkflowAnalysis();

                // 解析JSON
                JObject workflow;
                try
                {
                    workflow = JObject.Parse(jsonContent);
                }
                catch
                {
                    Console.WriteLine("JSON解析失败");
                    return null;
                }

                // 分析每个节点
                foreach (var kvp in workflow)
                {
                    if (kvp.Value is JObject nodeObj)
                    {
                        var nodeInfo = AnalyzeNode(kvp.Key, nodeObj);
                        analysis.AllNodes.Add(nodeInfo);

                        if (nodeInfo.UserInputs.Count > 0)
                        {
                            analysis.UserInputNodes.Add(nodeInfo);
                        }
                    }
                }

                // 推断工作流类型
                analysis.WorkflowType = InferWorkflowType(analysis.AllNodes);
                analysis.Description = GenerateDescription(analysis.AllNodes, analysis.UserInputNodes);

                return analysis;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析工作流失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 分析单个节点
        /// </summary>
        private static NodeInfo AnalyzeNode(string nodeId, JObject nodeObj)
        {
            var nodeInfo = new NodeInfo
            {
                NodeId = nodeId,
                ClassType = nodeObj["class_type"]?.ToString() ?? "",
                Title = nodeObj["_meta"]?["title"]?.ToString() ?? nodeId
            };

            // 解析输入参数
            if (nodeObj["inputs"] is JObject inputs)
            {
                foreach (var input in inputs)
                {
                    nodeInfo.Inputs[input.Key] = input.Value?.ToString() ?? "";
                }
            }

            // 检查是否需要用户输入
            if (NodeTypeDefinitions.ContainsKey(nodeInfo.ClassType))
            {
                var nodeTypeDef = NodeTypeDefinitions[nodeInfo.ClassType];
                foreach (var paramDef in nodeTypeDef.Parameters)
                {
                    // 检查节点输入中是否包含此参数，或者参数是必需的
                    if (nodeInfo.Inputs.ContainsKey(paramDef.Key) || paramDef.Value.IsRequired)
                    {
                        var currentValue = nodeInfo.Inputs.ContainsKey(paramDef.Key) ?
                            nodeInfo.Inputs[paramDef.Key] : paramDef.Value.DefaultValue;

                        var userInput = new UserInput
                        {
                            Name = paramDef.Key,
                            Type = paramDef.Value.Type,
                            DefaultValue = currentValue,
                            Description = paramDef.Value.Description,
                            IsRequired = paramDef.Value.IsRequired
                        };
                        nodeInfo.UserInputs.Add(userInput);
                    }
                }
            }
            else
            {
                // 对于未定义的节点类型，尝试从输入中推断参数
                foreach (var input in nodeInfo.Inputs)
                {
                    // 跳过连接类型的输入（通常是数组格式）
                    if (input.Value is string strValue && !strValue.StartsWith("["))
                    {
                        var userInput = new UserInput
                        {
                            Name = input.Key,
                            Type = InferParameterType(input.Value),
                            DefaultValue = input.Value,
                            Description = GenerateParameterDescription(input.Key),
                            IsRequired = false
                        };
                        nodeInfo.UserInputs.Add(userInput);
                    }
                }
            }

            return nodeInfo;
        }

        /// <summary>
        /// 推断参数类型
        /// </summary>
        private static string InferParameterType(object value)
        {
            if (value == null) return "string";

            var strValue = value.ToString();

            if (int.TryParse(strValue, out _))
                return "int";
            if (double.TryParse(strValue, out _))
                return "double";
            if (bool.TryParse(strValue, out _))
                return "bool";

            return "string";
        }

        /// <summary>
        /// 生成参数描述
        /// </summary>
        private static string GenerateParameterDescription(string paramName)
        {
            var descriptions = new Dictionary<string, string>
            {
                ["text"] = "文本内容",
                ["prompt"] = "提示词",
                ["image"] = "图片路径",
                ["width"] = "宽度",
                ["height"] = "高度",
                ["steps"] = "步数",
                ["cfg"] = "CFG值",
                ["seed"] = "随机种子",
                ["denoise"] = "去噪强度",
                ["strength"] = "强度",
                ["scale"] = "缩放",
                ["guidance"] = "引导强度",
                ["scheduler"] = "调度器",
                ["sampler"] = "采样器",
                ["model"] = "模型",
                ["checkpoint"] = "检查点",
                ["lora"] = "LoRA模型",
                ["vae"] = "VAE模型",
                ["clip"] = "CLIP模型",
                ["unet"] = "UNet模型"
            };

            foreach (var kvp in descriptions)
            {
                if (paramName.ToLower().Contains(kvp.Key))
                    return kvp.Value;
            }

            return paramName;
        }



        /// <summary>
        /// 推断工作流类型
        /// </summary>
        private static string InferWorkflowType(List<NodeInfo> nodes)
        {
            var nodeTypes = nodes.Select(n => n.ClassType).ToHashSet();

            if (nodeTypes.Contains("LoadImage"))
                return "图片处理";
            else if (nodeTypes.Contains("EmptyLatentImage"))
                return "文生图";
            else if (nodeTypes.Contains("UpscaleModelLoader"))
                return "图片放大";
            else
                return "通用工作流";
        }

        /// <summary>
        /// 生成描述
        /// </summary>
        private static string GenerateDescription(List<NodeInfo> allNodes, List<NodeInfo> userInputNodes)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"包含 {allNodes.Count} 个节点的ComfyUI工作流");
            sb.AppendLine($"需要用户输入 {userInputNodes.Count} 个参数");

            if (userInputNodes.Count > 0)
            {
                sb.AppendLine("用户输入参数:");
                foreach (var node in userInputNodes)
                {
                    foreach (var input in node.UserInputs)
                    {
                        sb.AppendLine($"  - {input.Description} ({input.Type})");
                    }
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 生成类名
        /// </summary>
        private static string GenerateClassName(string fileName)
        {
            // 移除特殊字符，转换为Pascal命名
            var sb = new StringBuilder();
            bool capitalizeNext = true;

            foreach (char c in fileName)
            {
                if (char.IsLetterOrDigit(c))
                {
                    if (capitalizeNext)
                    {
                        sb.Append(char.ToUpper(c));
                        capitalizeNext = false;
                    }
                    else
                    {
                        sb.Append(c);
                    }
                }
                else
                {
                    capitalizeNext = true;
                }
            }

            string result = sb.ToString();
            if (string.IsNullOrEmpty(result))
            {
                result = "UnknownWorkflow";
            }

            return result + "Workflow";
        }

        /// <summary>
        /// 生成包装类代码
        /// </summary>
        private static string GenerateWrapperClass(string className, WorkflowAnalysis analysis, string originalJson)
        {
            var sb = new StringBuilder();

            // 文件头
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using Newtonsoft.Json.Linq;");
            sb.AppendLine("using SaveDataService.Manage;");
            sb.AppendLine();
            sb.AppendLine("namespace SaveDataService.ComfyuiGate");
            sb.AppendLine("{");

            // 类注释
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {className} - {analysis.WorkflowType}");
            sb.AppendLine($"    /// {analysis.Description.Replace("\n", "\n    /// ")}");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {className}");
            sb.AppendLine("    {");

            // 原始工作流JSON
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 原始工作流JSON");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        private static readonly string OriginalWorkflowJson = @\"");
            sb.AppendLine(originalJson.Replace("\"", "\"\""));
            sb.AppendLine("        \";");
            sb.AppendLine();

            // 执行方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 执行工作流");
            sb.AppendLine("        /// </summary>");

            // 生成方法参数
            var parameters = new List<string>();
            var paramComments = new List<string>();

            foreach (var node in analysis.UserInputNodes)
            {
                foreach (var input in node.UserInputs)
                {
                    parameters.Add($"{input.Type} {ToCamelCase(input.Name)}");
                    paramComments.Add($"        /// <param name=\"{ToCamelCase(input.Name)}\">{input.Description}</param>");
                }
            }

            // 添加参数注释
            foreach (var comment in paramComments)
            {
                sb.AppendLine(comment);
            }
            sb.AppendLine("        /// <param name=\"serverUrl\">ComfyUI服务器地址（可选）</param>");
            sb.AppendLine("        /// <returns>执行结果</returns>");

            // 方法签名
            if (parameters.Count > 0)
            {
                sb.AppendLine($"        public static async Task<string> ExecuteAsync({string.Join(", ", parameters)}, string serverUrl = null)");
            }
            else
            {
                sb.AppendLine("        public static async Task<string> ExecuteAsync(string serverUrl = null)");
            }

            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine("                // 解析原始工作流");
            sb.AppendLine("                var workflow = JObject.Parse(OriginalWorkflowJson);");
            sb.AppendLine();

            // 参数替换逻辑
            if (analysis.UserInputNodes.Count > 0)
            {
                sb.AppendLine("                // 替换用户输入参数");
                foreach (var node in analysis.UserInputNodes)
                {
                    foreach (var input in node.UserInputs)
                    {
                        sb.AppendLine($"                if (workflow[\"{node.NodeId}\"]?[\"inputs\"]?[\"{input.Name}\"] != null)");
                        sb.AppendLine($"                    workflow[\"{node.NodeId}\"][\"inputs\"][\"{input.Name}\"] = {ToCamelCase(input.Name)};");
                        sb.AppendLine();
                    }
                }
            }

            sb.AppendLine("                // 使用ComfyUIManage执行工作流");
            sb.AppendLine("                var comfyUIManage = new ComfyUIManage();");
            sb.AppendLine("                var result = await comfyUIManage.ExecuteWorkflowAsync(workflow.ToString(), serverUrl);");
            sb.AppendLine("                return result;");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine($"                throw new Exception($\"{className}执行失败: {{ex.Message}}\", ex);");
            sb.AppendLine("            }");
            sb.AppendLine("        }");

            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 转换为Pascal命名
        /// </summary>
        private static string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var sb = new StringBuilder();
            bool capitalizeNext = true;

            foreach (char c in input)
            {
                if (char.IsLetterOrDigit(c))
                {
                    if (capitalizeNext)
                    {
                        sb.Append(char.ToUpper(c));
                        capitalizeNext = false;
                    }
                    else
                    {
                        sb.Append(c);
                    }
                }
                else
                {
                    capitalizeNext = true;
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 转换为camelCase命名
        /// </summary>
        private static string ToCamelCase(string input)
        {
            var pascalCase = ToPascalCase(input);
            if (string.IsNullOrEmpty(pascalCase))
                return pascalCase;

            return char.ToLower(pascalCase[0]) + pascalCase.Substring(1);
        }



        /// <summary>
        /// 生成测试类代码
        /// </summary>
        private static string GenerateTestClass(string className, WorkflowAnalysis analysis)
        {
            var sb = new StringBuilder();

            // 文件头
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using SaveDataService.ComfyuiGate;");
            sb.AppendLine();
            sb.AppendLine("namespace SaveDataService.ComfyuiGate");
            sb.AppendLine("{");

            // 类注释
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {className}测试类");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {className}Test");
            sb.AppendLine("    {");

            // 测试方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 测试工作流执行");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        public static async Task RunTestAsync()");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine($"                Console.WriteLine(\"=== {className} 测试开始 ===\");");
            sb.AppendLine();

            if (analysis.UserInputNodes.Count > 0)
            {
                // 生成测试参数
                var testParams = new List<string>();
                foreach (var node in analysis.UserInputNodes)
                {
                    foreach (var input in node.UserInputs)
                    {
                        testParams.Add(GetTestValue(input.Type, input.Name));
                    }
                }

                sb.AppendLine("                // 执行工作流");
                sb.AppendLine($"                var result = await {className}.ExecuteAsync({string.Join(", ", testParams)});");
            }
            else
            {
                sb.AppendLine("                // 执行工作流");
                sb.AppendLine($"                var result = await {className}.ExecuteAsync();");
            }

            sb.AppendLine("                Console.WriteLine($\"工作流执行结果: {result}\");");
            sb.AppendLine($"                Console.WriteLine(\"=== {className} 测试完成 ===\");");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine($"                Console.WriteLine($\"{className} 测试失败: {{ex.Message}}\");");
            sb.AppendLine("            }");
            sb.AppendLine("        }");

            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 获取测试值
        /// </summary>
        private static string GetTestValue(string type, string fieldName)
        {
            switch (type.ToLower())
            {
                case "int":
                    if (fieldName.Contains("width")) return "1024";
                    if (fieldName.Contains("height")) return "1024";
                    if (fieldName.Contains("steps")) return "20";
                    if (fieldName.Contains("seed")) return "42";
                    if (fieldName.Contains("batch")) return "1";
                    if (fieldName.Contains("clip_layer")) return "-1";
                    return "1";
                case "double":
                    if (fieldName.Contains("cfg")) return "7.0";
                    if (fieldName.Contains("denoise")) return "1.0";
                    if (fieldName.Contains("guidance")) return "3.5";
                    if (fieldName.Contains("strength")) return "1.0";
                    if (fieldName.Contains("percent")) return "0.5";
                    if (fieldName.Contains("b1")) return "1.1";
                    if (fieldName.Contains("b2")) return "1.2";
                    if (fieldName.Contains("s1")) return "0.9";
                    if (fieldName.Contains("s2")) return "0.2";
                    return "1.0";
                case "bool":
                    return "false";
                case "string":
                default:
                    if (fieldName.Contains("text") || fieldName.Contains("prompt")) return "\"a beautiful landscape, masterpiece, high quality\"";
                    if (fieldName.Contains("image")) return "\"input.png\"";
                    if (fieldName.Contains("sampler")) return "\"euler\"";
                    if (fieldName.Contains("scheduler")) return "\"normal\"";
                    if (fieldName.Contains("filename")) return "\"ComfyUI\"";
                    if (fieldName.Contains("ckpt") || fieldName.Contains("checkpoint")) return "\"model.safetensors\"";
                    if (fieldName.Contains("vae")) return "\"vae.safetensors\"";
                    if (fieldName.Contains("lora")) return "\"lora.safetensors\"";
                    if (fieldName.Contains("control")) return "\"control_net.safetensors\"";
                    if (fieldName.Contains("unet")) return "\"unet.safetensors\"";
                    if (fieldName.Contains("clip")) return "\"clip.safetensors\"";
                    if (fieldName.Contains("upscale")) return "\"nearest-exact\"";
                    if (fieldName.Contains("crop")) return "\"disabled\"";
                    if (fieldName.Contains("sampling")) return "\"eps\"";
                    return "\"default_value\"";
            }
        }
    }
}
